import time
import json
import random
import config

def create_device_attributes(conn, tenant_id, device_data_list):
    """Creates attribute_kv records for gateways and sensors.
    
    Args:
        conn: Database connection
        tenant_id: Tenant ID
        device_data_list: List of device data from device_seeder containing:
                         [{'device_id': ..., 'gateway_name': ..., 'gateway_serial': ..., 
                           'sensors': [{'sensor_name': ..., 'sensor_model': ...}, ...]}]
    """
    cursor = conn.cursor()
    
    # Disable trigger before inserting
    print("  🔧 Disabling attribute_kv triggers...")
    cursor.execute("ALTER TABLE attribute_kv DISABLE TRIGGER ALL")
    
    try:
        last_update_ts = int(time.time() * 1000)
        
        for device_data in device_data_list:
            device_id = device_data['device_id']
            gateway_name = device_data['gateway_name']
            gateway_serial = device_data['gateway_serial']
            tenant_no = device_data.get('tenant_no', '1')
            
            # Create gateway attribute
            create_gateway_attribute(cursor, device_id, gateway_name, gateway_serial, tenant_no, last_update_ts)
            
            # Create sensor attributes
            for sensor_data in device_data['sensors']:
                create_sensor_attribute(cursor, device_id, sensor_data, gateway_name, last_update_ts)
        
        print(f"Created attribute_kv records for {len(device_data_list)} devices")
        
    finally:
        # Re-enable trigger
        print("  🔧 Re-enabling attribute_kv triggers...")
        cursor.execute("ALTER TABLE attribute_kv ENABLE TRIGGER ALL")
        cursor.close()

def create_gateway_attribute(cursor, device_id, gateway_name, gateway_serial, tenant_no, last_update_ts):
    """Creates gateway attribute record."""
    
    # Gateway JSON attribute
    gateway_json = {
        "type": "SUTOgateway",
        "model": config.GATEWAY_MODEL,
        "TENANT_NO": str(tenant_no),
        "SN": gateway_serial,
        "FW": "1.39",
        "HW": "1.1"
    }
    
    query = """
    INSERT INTO attribute_kv (entity_type, entity_id, attribute_type, attribute_key, 
                             bool_v, str_v, long_v, dbl_v, json_v, last_update_ts)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    
    cursor.execute(query, (
        'DEVICE',           # entity_type
        device_id,          # entity_id
        'CLIENT_SCOPE',     # attribute_type
        gateway_name,       # attribute_key (e.g., S331/20000020)
        None,               # bool_v
        None,               # str_v
        None,               # long_v
        None,               # dbl_v
        json.dumps(gateway_json),  # json_v
        last_update_ts      # last_update_ts
    ))
    
    print(f"    Created gateway attribute: {gateway_name}")

def create_sensor_attribute(cursor, device_id, sensor_data, gateway_name, last_update_ts):
    """Creates sensor attribute record."""
    
    sensor_name = sensor_data['sensor_name']
    sensor_model = sensor_data['sensor_model']
    gateway_serial = gateway_name.split('/')[1]  # Extract serial from S331-20000020
    
    # Base sensor JSON
    sensor_json = {
        "type": "SUTOsensor",
        "model": sensor_model,
        "SN": gateway_serial,
        "FW": "2.7",
        "HW": "1.0",
        "GW": gateway_name
    }
    
    # Add channel-specific attributes based on sensor model
    if sensor_model == "S430":
        # S430 sensor channels and units
        sensor_json.update({
            "Velocity/Unit": "m/s",
            "Velocity/Resolution": 1,
            "Temperature/Unit": "°F",
            "Temperature/Resolution": 4,
            "Mass/Unit": "kg", 
            "Mass/Resolution": 3,
            "Power/Unit": "kW",
            "Power/Resolution": 4,
            "Pressure/Unit": "kPa(g)",
            "Pressure/Resolution": 0
        })
    elif sensor_model == "S401":
        # S401 sensor channels and units
        sensor_json.update({
            "Flow/Unit": "m³/h",
            "Flow/Resolution": 0,
            "Volume/Unit": "m³",
            "Volume/Resolution": 1,
            "Humidity/Unit": "mg/m³",
            "Humidity/Resolution": 2,
            "Dew point/Unit": "°C",
            "Dew point/Resolution": 2,
            "Velocity/Unit": "m/s",
            "Velocity/Resolution": 1
        })
    
    query = """
    INSERT INTO attribute_kv (entity_type, entity_id, attribute_type, attribute_key, 
                             bool_v, str_v, long_v, dbl_v, json_v, last_update_ts)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    
    cursor.execute(query, (
        'DEVICE',           # entity_type
        device_id,          # entity_id
        'CLIENT_SCOPE',     # attribute_type
        sensor_name,        # attribute_key (e.g., S430/20000020)
        None,               # bool_v
        None,               # str_v
        None,               # long_v
        None,               # dbl_v
        json.dumps(sensor_json),  # json_v
        last_update_ts      # last_update_ts
    ))
    
    print(f"    Created sensor attribute: {sensor_name}")
