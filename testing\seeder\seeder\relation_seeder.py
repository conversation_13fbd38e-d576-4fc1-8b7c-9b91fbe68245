def create_rule_relations(conn, rule_chain_id, rule_node_ids):
    """Creates relations between rule chain and rule nodes, and between rule nodes.
    
    Args:
        conn: Database connection
        rule_chain_id: The rule chain ID
        rule_node_ids: List of rule node IDs in the following order:
                      [save_timeseries_id, save_attributes_id, message_switch_id, 
                       log_rpc_id, log_other_id, rpc_request_id, device_profile_id]
    """
    cursor = conn.cursor()
    
    # Extract rule node IDs based on the order from rule_node_seeder
    save_timeseries_id = rule_node_ids[0]      # Save Timeseries
    save_attributes_id = rule_node_ids[1]      # Save Client Attributes  
    message_switch_id = rule_node_ids[2]       # Message Type Switch
    log_rpc_id = rule_node_ids[3]              # Log RPC from Device
    log_other_id = rule_node_ids[4]            # Log Other
    rpc_request_id = rule_node_ids[5]          # RPC Call Request
    device_profile_id = rule_node_ids[6]       # Device Profile Node (first node)
    
    # 1. Create RULE_CHAIN -> RULE_NODE "Contains" relations
    # Rule chain contains all rule nodes
    rule_chain_contains_relations = [
        (rule_chain_id, 'RULE_CHAIN', save_timeseries_id, 'RULE_NODE', 'RULE_CHAIN', 'Contains'),
        (rule_chain_id, 'RULE_CHAIN', save_attributes_id, 'RULE_NODE', 'RULE_CHAIN', 'Contains'),
        (rule_chain_id, 'RULE_CHAIN', message_switch_id, 'RULE_NODE', 'RULE_CHAIN', 'Contains'),
        (rule_chain_id, 'RULE_CHAIN', log_rpc_id, 'RULE_NODE', 'RULE_CHAIN', 'Contains'),
        (rule_chain_id, 'RULE_CHAIN', log_other_id, 'RULE_NODE', 'RULE_CHAIN', 'Contains'),
        (rule_chain_id, 'RULE_CHAIN', rpc_request_id, 'RULE_NODE', 'RULE_CHAIN', 'Contains'),
        (rule_chain_id, 'RULE_CHAIN', device_profile_id, 'RULE_NODE', 'RULE_CHAIN', 'Contains'),
    ]
    
    # 2. Create RULE_NODE -> RULE_NODE relations
    # Device Profile Node -> Message Type Switch (Success)
    # Message Type Switch -> other nodes based on message type
    rule_node_relations = [
        # Device Profile Node connects to Message Type Switch on Success
        (device_profile_id, 'RULE_NODE', message_switch_id, 'RULE_NODE', 'RULE_NODE', 'Success'),
        
        # Message Type Switch routes to different nodes based on message type
        (message_switch_id, 'RULE_NODE', log_other_id, 'RULE_NODE', 'RULE_NODE', 'Other'),
        (message_switch_id, 'RULE_NODE', save_attributes_id, 'RULE_NODE', 'RULE_NODE', 'Post attributes'),
        (message_switch_id, 'RULE_NODE', save_timeseries_id, 'RULE_NODE', 'RULE_NODE', 'Post telemetry'),
        (message_switch_id, 'RULE_NODE', log_rpc_id, 'RULE_NODE', 'RULE_NODE', 'RPC Request from Device'),
        (message_switch_id, 'RULE_NODE', rpc_request_id, 'RULE_NODE', 'RULE_NODE', 'RPC Request to Device'),
    ]
    
    # Combine all relations
    all_relations = rule_chain_contains_relations + rule_node_relations
    
    # Insert all relations
    for relation in all_relations:
        from_id, from_type, to_id, to_type, relation_type_group, relation_type = relation
        
        query = """
        INSERT INTO relation (from_id, from_type, to_id, to_type, relation_type_group, relation_type, additional_info)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(query, (
            from_id,
            from_type,
            to_id,
            to_type,
            relation_type_group,
            relation_type,
            None  # additional_info
        ))
    
    cursor.close()
    
    print(f"Created {len(all_relations)} relations for rule chain: {rule_chain_id}")
    print(f"  - {len(rule_chain_contains_relations)} RULE_CHAIN -> RULE_NODE 'Contains' relations")
    print(f"  - {len(rule_node_relations)} RULE_NODE -> RULE_NODE flow relations")
    
    return len(all_relations)
