import os
import random

# Database credentials
DB_NAME = "thingsboard"
DB_USER = "postgres"
DB_PASSWORD = "postgres"
DB_HOST = "localhost"
DB_PORT = "5432"

# Number of tenants to create
NUM_TENANTS = 1

# Data generation parameters
GATEWAYS_PER_TENANT = 1
SENSORS_PER_GATEWAY = 2
CHANNELS_PER_SENSOR = 5  # channels per measurement point
USERS_PER_TENANT = 3
FACTORIES_PER_TENANT = 2
CAS_LOCATIONS_PER_FACTORY = 1
MEASUREMENT_LOCATIONS_PER_CAS = 1
MEASUREMENT_POINTS_PER_LOCATION = 2

# History data generation performance settings
HISTORY_BATCH_SIZE = 20000  # Number of records per batch
HISTORY_LOG_INTERVAL = 100000  # Log progress every N records
HISTORY_DATA_INTERVAL_SECONDS = 5  # Time interval between data points
HISTORY_START_DATE = '2025-04-10'  # Start date for history data
HISTORY_END_DATE = '2025-07-10'  # End date for history data




#########################
# DONT TOUCH THE REST
# Device Models and Serial Numbers
GATEWAY_MODEL = 'S331'
SENSOR_MODELS = ['S401', 'S430', 'S120', 'S121', 'S122', 'S123']
GATEWAY_SERIAL_BASE = 20001000
TENANT_SERIAL_BASE = 1751429774

# Default Foreign Keys & IDs
DEFAULT_TENANT_PROFILE_ID = '790f9240-5c2e-11f0-8845-07378cc40f59'
PUBLIC_CUSTOMER_ID = '13814000-1dd2-11b2-8080-808080808080'

# Channel and Unit Mapping (ensures logical data)
CHANNEL_UNITS_MAP = {
    'Concentration': ['cn/m³'],
    'Dew point': ['°C Td'],
    'Energy': ['kWh'],
    'Flow': ['l/s', 'm³/h'],
    'Humidity': ['mg/m³'],
    'Mass': ['kg'],
    'Power': ['kW'],
    'Pressure': ['kPa(g)'],
    'Temperature': ['°F', '°C'],
    'Velocity': ['m/s'],
    'Voltage': ['V'],
    'Volume': ['m³']
}

# Extract unique lists for backward compatibility
unique_channel_names = list(CHANNEL_UNITS_MAP.keys())

# Default values for new tables
DEFAULT_DEVICE_PROFILE_DATA = '{"alarms": null, "configuration": {"type": "DEFAULT"}, "provisionConfiguration": {"type": "DISABLED"}, "transportConfiguration": {"type": "DEFAULT"}}'
DEFAULT_PASSWORD_HASH = '$2a$10$6hX6wG/zevnwT3x5TEoeGOKGLqfF5ZCBYI0Qw5r14N/aEjaxYEQqS'

# Permission features list
PERMISSION_FEATURES = ['Dashboard', 'Live View', 'Alarms', 'Analysis', 'Assets', 'Calendar', 'Reports', 'Factory', 'Virtual Channel', 'Gateway', 'User']

# Global counters (will be managed by the script)
_gateway_serial_counter = GATEWAY_SERIAL_BASE
_tenant_serial_counter = TENANT_SERIAL_BASE
_default_tenant_profile_id = DEFAULT_TENANT_PROFILE_ID
_key_id_counter = 1

def get_next_gateway_serial():
    """Get the next gateway serial number."""
    global _gateway_serial_counter
    serial = _gateway_serial_counter
    _gateway_serial_counter += 1
    return serial

def get_next_tenant_serial():
    """Get the next tenant serial number."""
    global _tenant_serial_counter
    serial = _tenant_serial_counter
    _tenant_serial_counter += 1
    return serial

def get_next_key_id(conn):
    """Get the next key_id for ts_kv_dictionary."""
    global _key_id_counter
    cursor = conn.cursor()
    cursor.execute("SELECT nextval('ts_kv_dictionary_key_id_seq'::regclass)")
    _key_id_counter = cursor.fetchone()[0]
    key_id = _key_id_counter
    cursor.close()
    return key_id

def get_default_tenant_profile_id():
    """Get the next gateway serial number."""
    global _default_tenant_profile_id
    return _default_tenant_profile_id

def initialize_counters(conn):
    """Initialize all counters based on existing data."""
    global _key_id_counter, _gateway_serial_counter, _tenant_serial_counter, _default_tenant_profile_id
    cursor = conn.cursor()
    try:
        # Initialize key_id counter
        cursor.execute("SELECT nextval('ts_kv_dictionary_key_id_seq'::regclass)")
        _key_id_counter = cursor.fetchone()[0]

        # Initialize gateway serial counter
        cursor.execute("SELECT COALESCE(MAX(CAST(sn AS INTEGER)), 20000999) + 1 FROM device WHERE model = %s", (GATEWAY_MODEL,))
        result = cursor.fetchone()[0]
        if result:
            _gateway_serial_counter = result

        # Initialize tenant serial counter
        cursor.execute(f"SELECT COALESCE(MAX(CAST(tenant_no AS INTEGER)), {TENANT_SERIAL_BASE}) + 1 FROM tenant")
        result = cursor.fetchone()[0]
        if result:
            _tenant_serial_counter = result

        # Initialize default tenant profile id
        cursor.execute("SELECT id FROM tenant_profile WHERE name = 'Default'")
        result = cursor.fetchone()
        if result:
            _default_tenant_profile_id = result[0]

    except Exception as e:
        print(f"Warning: Could not initialize counters: {e}")
        _key_id_counter = 1
        _gateway_serial_counter = GATEWAY_SERIAL_BASE
        _tenant_serial_counter = TENANT_SERIAL_BASE
    finally:
        cursor.close()
