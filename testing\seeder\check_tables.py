from database import get_db_connection

def check_tables():
    """Check if required tables exist in the database."""
    required_tables = [
        'tenant',
        'tb_user',
        'config_tenant_factory',
        'config_cas_location',
        'config_measurement_location',
        'config_measurement_point',
        'device',
        'gateway',
        'master_sensors',
        'master_channels',
        'config_tenant_gateway_sensors_channels',
        'history_report_data',
        # New tables
        'ts_kv_dictionary',
        'config_tb_user_role_permission',
        'device_credentials',
        'device_profile',
        'license_management',
        'license_type_management',
        'master_gateway',
        'user_credentials'
    ]
    
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    print("🔍 Checking for required tables...")
    missing_tables = []
    
    for table in required_tables:
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = %s
            );
        """, (table,))
        
        exists = cursor.fetchone()[0]
        if exists:
            print(f"✅ Table '{table}' exists")
        else:
            print(f"❌ Table '{table}' is missing")
            missing_tables.append(table)
    
    cursor.close()
    conn.close()
    
    if missing_tables:
        print(f"\n❌ Missing tables: {', '.join(missing_tables)}")
        return False
    else:
        print("\n✅ All required tables exist!")
        return True

if __name__ == "__main__":
    check_tables()
