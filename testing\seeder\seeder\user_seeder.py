import uuid
import time
import config
from . import credentials_seeder

def create_users(conn, tenant_id):
    """Creates multiple users for a given tenant and returns list of user_ids."""
    all_users = []

    # Create 1 tenant owner
    owner_id = create_user(conn, tenant_id, 'TENANT_ADMIN', 'Owner')
    all_users.append({'user_id': owner_id, 'authority': 'TENANT_ADMIN', 'role_type': 'Owner'})

    # Create tenant users
    for i in range(config.USERS_PER_TENANT):
        user_id = create_user(conn, tenant_id, 'TENANT_ADMIN', 'User')
        all_users.append({'user_id': user_id, 'authority': 'TENANT_ADMIN', 'role_type': 'Standard'})

    return all_users

def create_user(conn, tenant_id, authority, role_type):
    """Creates a new user for a given tenant and returns the user_id."""
    cursor = conn.cursor()
    user_id = str(uuid.uuid4())
    created_time = int(time.time() * 1000)

    # Generate a unique email and name for the user
    email = f"user_{created_time}@example.com"
    first_name = f"User_{created_time}"

    query = """
    INSERT INTO tb_user (id, additional_info, created_time, tenant_id, customer_id, email, first_name, authority, search_text)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    cursor.execute(query, (user_id, '{"description":""}', created_time, tenant_id, config.PUBLIC_CUSTOMER_ID,
                          email, first_name, authority, email))

    # Create user credentials
    credentials_seeder.create_user_credentials(conn, user_id)

    cursor.close()

    print(f"Created {role_type} user with ID: {user_id} for tenant: {tenant_id}")
    return user_id
