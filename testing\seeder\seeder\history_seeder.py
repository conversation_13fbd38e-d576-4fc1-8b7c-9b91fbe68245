import uuid
import random
from datetime import datetime, timedelta
import psycopg2.extras
import time
import config

def create_history_data(conn, tenant_id, channel_data_list, start_date, end_date):
    """Creates history data for all channels of a tenant with high-performance batch processing.

    Args:
        conn: Database connection
        tenant_id: Tenant ID
        channel_data_list: List of dicts with channel info from device_seeder
        start_date: Start date string
        end_date: End date string
    """
    if not channel_data_list:
        print("No channel data provided, skipping history data creation")
        return

    cursor = conn.cursor()

    # Parse dates
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d')

    # Calculate total data points for progress tracking
    time_delta = end_dt - start_dt
    total_intervals = int(time_delta.total_seconds() / config.HISTORY_DATA_INTERVAL_SECONDS)
    total_data_points = total_intervals * len(channel_data_list)

    print(f"📊 Starting history data generation for tenant: {tenant_id}")
    print(f"    Date range: {start_date} to {end_date}")
    print(f"    Channels: {len(channel_data_list)}")
    print(f"    Time intervals: {total_intervals:,}")
    print(f"    Total data points: {total_data_points:,}")
    print(f"    Batch size: {config.HISTORY_BATCH_SIZE:,}")

    # Performance optimizations
    optimize_database_for_bulk_insert(cursor)

    try:
        # Generate and insert data in batches
        batch_insert_history_data(cursor, channel_data_list, start_dt, end_dt, total_data_points)

        # Note: Transaction management is now handled by the calling script
        print("✅ History data generation completed successfully")

    except Exception as e:
        print(f"❌ Error during history data generation: {e}")
        # Note: Transaction rollback is now handled by the calling script
        raise

    finally:
        # Restore database optimizations
        restore_database_optimizations(cursor)
        cursor.close()

def optimize_database_for_bulk_insert(cursor):
    """Apply database optimizations for bulk insert performance."""
    print("🔧 Applying database optimizations...")

    try:
        # Increase work_mem for better sorting performance
        cursor.execute("SET work_mem = '256MB'")
        print("✅ Database optimizations applied")

    except Exception as e:
        print(f"⚠️ Warning: Could not apply optimizations: {e}")

def restore_database_optimizations(cursor):
    """Restore database settings to defaults."""
    print("🔧 Restoring database settings...")

    try:
        cursor.execute("SET work_mem = DEFAULT")
        print("✅ Database settings restored")

    except Exception as e:
        print(f"⚠️ Warning: Could not restore settings: {e}")

def batch_insert_history_data(cursor, channel_data_list, start_dt, end_dt, total_data_points):
    """Insert history data using batch processing for optimal performance."""

    batch_data = []
    data_points_created = 0
    batch_count = 0
    start_time = time.time()

    current_dt = start_dt
    while current_dt < end_dt:
        for channel_data in channel_data_list:
            # Prepare data tuple
            history_id = str(uuid.uuid4())
            created_time = int(current_dt.timestamp() * 1000)
            data_value = str(random.randint(-20, 100))

            batch_data.append((
                history_id,
                created_time,
                channel_data['device_id'],  # entity_id is the gateway device_id
                channel_data['key_id'],     # key_id from ts_kv_dictionary
                f"\"{channel_data['sensor_name']}\"",
                channel_data['channel_name'],
                data_value
            ))

            data_points_created += 1

            # Execute batch when batch size is reached
            if len(batch_data) >= config.HISTORY_BATCH_SIZE:
                execute_batch_insert(cursor, batch_data)
                batch_count += 1
                batch_data = []

                # Log progress
                if data_points_created % config.HISTORY_LOG_INTERVAL == 0:
                    elapsed_time = time.time() - start_time
                    progress_pct = (data_points_created / total_data_points) * 100
                    rate = data_points_created / elapsed_time if elapsed_time > 0 else 0

                    print(f"📈 Progress: {data_points_created:,}/{total_data_points:,} "
                          f"({progress_pct:.1f}%) - {rate:.0f} records/sec - "
                          f"Batch #{batch_count}")

        current_dt += timedelta(seconds=config.HISTORY_DATA_INTERVAL_SECONDS)

    # Insert remaining data in final batch
    if batch_data:
        execute_batch_insert(cursor, batch_data)
        batch_count += 1

    # Final statistics
    elapsed_time = time.time() - start_time
    avg_rate = data_points_created / elapsed_time if elapsed_time > 0 else 0

    print(f"✅ History data generation completed:")
    print(f"    Total records: {data_points_created:,}")
    print(f"    Total batches: {batch_count}")
    print(f"    Total time: {elapsed_time:.1f} seconds")
    print(f"    Average rate: {avg_rate:.0f} records/sec")

def execute_batch_insert(cursor, batch_data):
    """Execute a batch insert using psycopg2's execute_values for optimal performance."""

    query = """
    INSERT INTO history_report_data
    (id, created_time, entity_id, key_id, sensor_name, channel_name, data_value)
    VALUES %s
    """

    try:
        # Use execute_values for high-performance batch insert
        psycopg2.extras.execute_values(
            cursor,
            query,
            batch_data,
            template=None,
            page_size=config.HISTORY_BATCH_SIZE
        )

    except Exception as e:
        print(f"❌ Error in batch insert: {e}")
        raise
