from database import get_db_connection
import config
from seeder import (tenant_seeder, user_seeder, factory_seeder, device_seeder,
                   history_seeder, license_seeder, permission_seeder,
                   device_profile_seeder, rule_chain_seeder, rule_node_seeder,
                   relation_seeder, attribute_seeder)
import traceback

def main():
    """Main function to seed the database."""
    print("🚀 Starting database seeding process...")

    # Initialize the key_id counter from existing data
    init_conn = get_db_connection()
    if not init_conn:
        print("❌ Failed to establish initial database connection")
        return

    config.initialize_counters(init_conn)
    init_conn.close()

    print(f"📊 Initialized counters - key_id: {config._key_id_counter}, gateway_serial: {config._gateway_serial_counter}, tenant_serial: {config._tenant_serial_counter}")

    for i in range(config.NUM_TENANTS):
        print(f"\n🔄 Seeding data for Tenant {i+1}/{config.NUM_TENANTS}")

        # Create a new database connection and cursor for each tenant
        # to ensure transactional integrity and avoid cursor-related issues.
        tenant_conn = get_db_connection()
        if not tenant_conn:
            print(f"❌ Failed to create connection for tenant {i+1}")
            continue

        try:
            print("  📝 Creating tenant...")
            tenant_id, tenant_no, title = tenant_seeder.create_tenant(tenant_conn)

            print("  👤 Creating users...")
            users_list = user_seeder.create_users(tenant_conn, tenant_id)
            owner_user_id = users_list[0]['user_id']  # First user is the owner

            print("  📄 Creating device profile...")
            device_profile_id = device_profile_seeder.create_device_profile(tenant_conn, tenant_id)

            print("  📜 Creating licenses...")
            license_seeder.create_license_management(tenant_conn, tenant_id)
            license_seeder.create_license_type_management(tenant_conn, tenant_id)

            print("  🔐 Creating permissions...")
            permission_seeder.create_user_role_permissions(tenant_conn, tenant_id, users_list)

            print("  🏭 Creating factories...")
            measurement_points = factory_seeder.create_factories(tenant_conn, tenant_id, owner_user_id)

            print("  📡 Creating devices...")
            channel_data_list, device_data_list = device_seeder.create_devices(tenant_conn, tenant_id, device_profile_id, tenant_no, title, measurement_points)

            print("  📊 Creating history data...")
            # Create history data with optimized batch processing
            history_seeder.create_history_data(tenant_conn, tenant_id, channel_data_list, config.HISTORY_START_DATE, config.HISTORY_END_DATE)

            print("  🔗 Creating rule chains...")
            rule_chain_id, first_rule_node_id = rule_chain_seeder.create_rule_chain(tenant_conn, tenant_id)

            print("  📋 Creating rule nodes...")
            rule_node_ids = rule_node_seeder.create_rule_nodes(tenant_conn, rule_chain_id, first_rule_node_id)

            print("  🔄 Creating rule relations...")
            relation_seeder.create_rule_relations(tenant_conn, rule_chain_id, rule_node_ids)

            print("  📋 Creating device attributes...")
            attribute_seeder.create_device_attributes(tenant_conn, tenant_id, device_data_list)

            tenant_conn.commit()
            print(f"✅ Successfully seeded tenant {i+1}")

        except Exception as e:
            print(f"❌ An error occurred while seeding tenant {i+1}: {e}")
            print("📋 Full error traceback:")
            traceback.print_exc()
            tenant_conn.rollback()
        finally:
            tenant_conn.close()

        print("-" * 50)

    print("🎉 Database seeding process completed!")

if __name__ == "__main__":
    main()
