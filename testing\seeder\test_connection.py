import sys
import traceback

try:
    from database import get_db_connection
    print("✅ Successfully imported database module")
except Exception as e:
    print(f"❌ Failed to import database module: {e}")
    traceback.print_exc()
    sys.exit(1)

def test_connection():
    """Test database connection."""
    try:
        print("🔄 Attempting database connection...")
        conn = get_db_connection()
        if conn:
            print("✅ Database connection successful!")

            # Test a simple query
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()
            print(f"PostgreSQL version: {version[0]}")

            cursor.close()
            conn.close()
            return True
        else:
            print("❌ Database connection failed!")
            return False
    except Exception as e:
        print(f"❌ Error during connection test: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting database connection test...")
    result = test_connection()
    if result:
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed!")
        sys.exit(1)
