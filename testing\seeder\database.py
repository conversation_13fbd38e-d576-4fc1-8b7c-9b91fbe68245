import psycopg2
import config

def get_db_connection():
    """Establishes a connection to the PostgreSQL database."""
    try:
        conn = psycopg2.connect(
            dbname=config.DB_NAME,
            user=config.DB_USER,
            password=config.DB_PASSWORD,
            host=config.DB_HOST,
            port=config.DB_PORT
        )
        print("Database connection successful")
        return conn
    except psycopg2.Error as e:
        print(f"Error connecting to database: {e}")
        return None
