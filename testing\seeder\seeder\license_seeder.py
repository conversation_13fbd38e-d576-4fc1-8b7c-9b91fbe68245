import uuid
import time
from datetime import datetime, timedelta

def create_license_management(conn, tenant_id):
    """Creates license management record for a given tenant."""
    cursor = conn.cursor()
    
    license_id = str(uuid.uuid4())
    created_time = int(time.time() * 1000)
    
    # Calculate dates
    start_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    expiry_date = (datetime.now() + timedelta(days=365)).strftime('%Y-%m-%d')
    
    query = """
    INSERT INTO license_management (id, created_time, tenant_id, status, start_date, 
                                   license_period, expiry_date, valid_for_days, no_channels, no_users)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    
    cursor.execute(query, (
        license_id,
        created_time,
        tenant_id,
        'Active',
        start_date,
        '365',
        expiry_date,
        '365',
        '50',
        '100'
    ))
    
    cursor.close()
    print(f"Created license management for tenant: {tenant_id}")
    return license_id

def create_license_type_management(conn, tenant_id):
    """Creates license type management record for a given tenant."""
    cursor = conn.cursor()

    license_type_id = str(uuid.uuid4())
    created_time = int(time.time() * 1000)

    query = """
    INSERT INTO license_type_management (id, created_time, tenant_id, type_id)
    VALUES (%s, %s, %s, %s)
    """

    cursor.execute(query, (
        license_type_id,
        created_time,
        tenant_id,
        1  # type_id
    ))

    cursor.close()
    print(f"Created license type management for tenant: {tenant_id}")
    return license_type_id
