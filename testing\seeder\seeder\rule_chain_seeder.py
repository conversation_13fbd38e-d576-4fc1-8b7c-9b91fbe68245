import uuid
import time

def create_rule_chain(conn, tenant_id):
    """Creates a root rule chain for a given tenant and returns the rule_chain_id and first_rule_node_id."""
    cursor = conn.cursor()
    
    rule_chain_id = str(uuid.uuid4())
    first_rule_node_id = str(uuid.uuid4())  # This will be the Device Profile Node
    created_time = int(time.time() * 1000)
    
    query = """
    INSERT INTO rule_chain (id, created_time, additional_info, configuration, name, type, 
                           first_rule_node_id, root, debug_mode, search_text, tenant_id)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    
    cursor.execute(query, (
        rule_chain_id,
        created_time,
        'null',  # additional_info
        'null',  # configuration
        'Root Rule Chain',  # name
        'CORE',  # type
        first_rule_node_id,  # first_rule_node_id
        True,  # root
        False,  # debug_mode
        'root rule chain',  # search_text
        tenant_id
    ))
    
    cursor.close()
    
    print(f"Created rule chain with ID: {rule_chain_id} for tenant: {tenant_id}")
    return rule_chain_id, first_rule_node_id
