import uuid
import time
import config

def create_device_profile(conn, tenant_id):
    """Creates a device profile for a given tenant."""
    cursor = conn.cursor()
    
    device_profile_id = str(uuid.uuid4())
    created_time = int(time.time() * 1000)
    
    query = """
    INSERT INTO device_profile (id, created_time, tenant_id, name, type, transport_type, 
                               provision_type, is_default, profile_data, description, search_text)
    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """
    
    cursor.execute(query, (
        device_profile_id,
        created_time,
        tenant_id,
        'default',
        'DEFAULT',
        'DEFAULT',
        'DISABLED',
        True,  # is_default
        config.DEFAULT_DEVICE_PROFILE_DATA,
        'Default device profile',
        'default'
    ))
    
    cursor.close()
    print(f"Created device profile for tenant: {tenant_id}")
    return device_profile_id
