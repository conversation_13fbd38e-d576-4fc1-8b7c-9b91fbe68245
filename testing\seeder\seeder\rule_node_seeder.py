import uuid
import time

def create_rule_nodes(conn, rule_chain_id, first_rule_node_id):
    """Creates all rule nodes for a given rule chain.

    Returns rule node IDs in consistent order:
    [save_timeseries_id, save_attributes_id, message_switch_id,
     log_rpc_id, log_other_id, rpc_request_id, device_profile_id]
    """
    cursor = conn.cursor()
    created_time = int(time.time() * 1000)

    # Generate UUIDs for each node to ensure consistent ordering
    save_timeseries_id = str(uuid.uuid4())
    save_attributes_id = str(uuid.uuid4())
    message_switch_id = str(uuid.uuid4())
    log_rpc_id = str(uuid.uuid4())
    log_other_id = str(uuid.uuid4())
    rpc_request_id = str(uuid.uuid4())
    device_profile_id = first_rule_node_id  # This is provided as the first node

    # Define all rule nodes with specific IDs
    rule_nodes = [
        {
            'id': save_timeseries_id,
            'additional_info': '{"layoutX":824,"layoutY":156}',
            'configuration': '{"defaultTTL":0}',
            'type': 'org.thingsboard.rule.engine.telemetry.TbMsgTimeseriesNode',
            'name': 'Save Timeseries',
            'search_text': 'save timeseries'
        },
        {
            'id': save_attributes_id,
            'additional_info': '{"layoutX":825,"layoutY":52}',
            'configuration': '{"scope":"CLIENT_SCOPE","notifyDevice":"false"}',
            'type': 'org.thingsboard.rule.engine.telemetry.TbMsgAttributesNode',
            'name': 'Save Client Attributes',
            'search_text': 'save client attributes'
        },
        {
            'id': message_switch_id,
            'additional_info': '{"layoutX":347,"layoutY":149}',
            'configuration': '{"version":0}',
            'type': 'org.thingsboard.rule.engine.filter.TbMsgTypeSwitchNode',
            'name': 'Message Type Switch',
            'search_text': 'message type switch'
        },
        {
            'id': log_rpc_id,
            'additional_info': '{"layoutX":825,"layoutY":266}',
            'configuration': '{"jsScript":"return \'\\\\nIncoming message:\\\\n\' + JSON.stringify(msg) + \'\\\\nIncoming metadata:\\\\n\' + JSON.stringify(metadata);"}',
            'type': 'org.thingsboard.rule.engine.action.TbLogNode',
            'name': 'Log RPC from Device',
            'search_text': 'log rpc from device'
        },
        {
            'id': log_other_id,
            'additional_info': '{"layoutX":825,"layoutY":379}',
            'configuration': '{"jsScript":"return \'\\\\nIncoming message:\\\\n\' + JSON.stringify(msg) + \'\\\\nIncoming metadata:\\\\n\' + JSON.stringify(metadata);"}',
            'type': 'org.thingsboard.rule.engine.action.TbLogNode',
            'name': 'Log Other',
            'search_text': 'log other'
        },
        {
            'id': rpc_request_id,
            'additional_info': '{"layoutX":825,"layoutY":468}',
            'configuration': '{"timeoutInSeconds":60}',
            'type': 'org.thingsboard.rule.engine.rpc.TbSendRPCRequestNode',
            'name': 'RPC Call Request',
            'search_text': 'rpc call request'
        },
        {
            'id': device_profile_id,  # This is the first rule node
            'additional_info': '{"description":"Process incoming messages from devices with the alarm rules defined in the device profile. Dispatch all incoming messages with \\"Success\\" relation type.","layoutX":204,"layoutY":240}',
            'configuration': '{"persistAlarmRulesState":false,"fetchAlarmRulesStateOnStart":false}',
            'type': 'org.thingsboard.rule.engine.profile.TbDeviceProfileNode',
            'name': 'Device Profile Node',
            'search_text': 'device profile node'
        }
    ]
    
    # Insert all rule nodes
    for node in rule_nodes:
        query = """
        INSERT INTO rule_node (id, created_time, rule_chain_id, additional_info, configuration, 
                              type, name, debug_mode, search_text)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        cursor.execute(query, (
            node['id'],
            created_time,
            rule_chain_id,
            node['additional_info'],
            node['configuration'],
            node['type'],
            node['name'],
            False,  # debug_mode
            node['search_text']
        ))
    
    cursor.close()

    print(f"Created {len(rule_nodes)} rule nodes for rule chain: {rule_chain_id}")

    # Return rule node IDs in consistent order for relation seeder
    return [save_timeseries_id, save_attributes_id, message_switch_id,
            log_rpc_id, log_other_id, rpc_request_id, device_profile_id]
